import gql from 'graphql-tag'

export const ReadCrewMembers_LogBookEntrySections = gql`
    query readCrewMembers_LogBookEntrySections(
        $filter: CrewMembers_LogBookEntrySectionFilterFields = {}
    ) {
        readCrewMembers_LogBookEntrySections(filter: $filter) {
            nodes {
                id
                punchIn
                punchOut
                archived
                dutyHours
                workDetails
                crewMemberID
                crewMember {
                    id
                    firstName
                    surname
                    trainingSessionsDue {
                        nodes {
                            id
                            dueDate
                            memberID
                            member {
                                id
                                firstName
                                surname
                            }
                            vesselID
                            vessel {
                                id
                                title
                            }
                            trainingTypeID
                            trainingType {
                                id
                                title
                            }
                        }
                    }
                }
                dutyPerformedID
                dutyPerformed {
                    id
                    title
                    abbreviation
                }
                logBookEntryID
                logBookEntry {
                    id
                    startDate
                    vehicleID
                    vehicle {
                        id
                        title
                    }
                }
            }
        }
    }
`

export const ReadCrewWelfare_LogBookEntrySections = gql`
    query ReadCrewWelfare_LogBookEntrySections($id: [ID]!) {
        readCrewWelfare_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                fitness
                safetyActions
                waterQuality
                imSafe
            }
        }
    }
`

export const ReadFuel_LogBookEntrySections = gql`
    query ReadFuel_LogBookEntrySections($id: [ID]!) {
        readFuel_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                fuelTankStartStops {
                    nodes {
                        id
                        start
                        end
                        fuelType
                        comments
                        fuelTankID
                    }
                }
            }
        }
    }
`

export const ReadOneCustomisedLogBookConfig = gql`
    query ReadOneCustomisedLogBookConfig($id: ID!) {
        readOneCustomisedLogBookConfig(
            filter: { customisedLogBookID: { eq: $id } }
        ) {
            id
            title
            lastEdited
            customisedComponentCategories
            customisedLogBook {
                id
                title
            }
            customisedLogBookComponents {
                nodes {
                    id
                    title
                    active
                    sortOrder
                    subView
                    subViewTitle
                    subFields
                    customEntryType
                    componentClass
                    category
                    disclaimer {
                        id
                        disclaimerText
                    }
                    customisedComponentFields(
                        sort: {
                            sortOrder: ASC
                            customisedFieldTitle: ASC
                            fieldName: ASC
                        }
                        limit: 500
                    ) {
                        pageInfo {
                            totalCount
                        }
                        nodes {
                            id
                            fieldName
                            status
                            sortOrder
                            description
                            customisedFieldTitle
                            customisedFieldType
                            customisedEngineTypes
                        }
                    }
                }
            }
            policies {
                nodes {
                    id
                    fileFilename
                    name
                    title
                }
            }
        }
    }
`

export const ReadOneLogBookEntry = gql`
    query ReadOneLogBookEntry($logbookEntryId: ID!) {
        readOneLogBookEntry(filter: { id: { eq: $logbookEntryId } }) {
            id
            masterID
            state
            className
            startDate
            endDate
            fuelLevel
            logBookID
            createdByID
            signOffCommentID
            signOffSignatureID
            clientID
            lockedDate
            lastConfig
            fuelLog {
                nodes {
                    id
                    fuelAdded
                    fuelBefore
                    fuelAfter
                    date
                    costPerLitre
                    totalCost
                    fuelTank {
                        id
                        capacity
                        safeFuelCapacity
                        currentLevel
                        title
                    }
                }
            }
            engineStartStop {
                nodes {
                    id
                    hoursStart
                    engineID
                    engine {
                        id
                        title
                        currentHours
                    }
                }
            }
            logBook {
                id
                title
                componentConfig
            }
            master {
                id
                firstName
                surname
            }
            logBookEntrySections {
                nodes {
                    id
                    className
                }
            }
            vehicle {
                id
                seaLogsMembers {
                    nodes {
                        id
                        firstName
                        surname
                        archived
                        primaryDutyID
                    }
                }
            }
        }
    }
`

export const ReadSectionMemberComments = gql`
    query ReadSectionMemberComments(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SectionMemberCommentFilterFields = {}
    ) {
        readSectionMemberComments(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            nodes {
                id
                commentType
                fieldName
                comment
                logBookEntrySectionID
                hideComment
            }
        }
    }
`

// LogBookSignOff_LogBookEntrySection
export const ReadLogBookSignOff_LogBookEntrySections = gql`
    query ReadLogBookSignOff_LogBookEntrySections($id: [ID]!) {
        readLogBookSignOff_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                review
                safetyEquipmentCheck
                forecastAccuracy
                ais
                navigationLightsAndShapes
                electronicNavigationalAids
                mainEngines
                auxiliarySystems
                fuelAndOil
                bilgeSystems
                power
                batteryMaintenance
                circuitInspections
                mooringAndAnchoring
                cargoAndAccessEquipment
                hatchesAndWatertightDoors
                galleyAppliances
                wasteManagement
                ventilationAndAirConditioning
                emergencyReadiness
                environmentalCompliance
                fuelStart
                completedTime
                endLocationID
                endLocation {
                    id
                    lat
                    long
                    geoLocationID
                    geoLocation {
                        id
                        title
                        lat
                        long
                    }
                }
                sectionSignatureID
                sectionSignature {
                    id
                    signatureData
                }
            }
        }
    }
`

// Supernumerary_LogBookEntrySection
export const ReadSupernumerary_LogBookEntrySections = gql`
    query ReadSupernumerary_LogBookEntrySections($id: [ID]!) {
        readSupernumerary_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                firstName
                surname
            }
        }
    }
`

// TripReport_LogBookEntrySection
export const ReadTripReport_LogBookEntrySections = gql`
    query ReadTripReport_LogBookEntrySections($id: [ID]!) {
        readTripReport_LogBookEntrySections(
            filter: { id: { in: $id } }
            sort: { created: ASC }
        ) {
            nodes {
                id
                archived
                departTime
                totalVehiclesCarried
                arriveTime
                arrive
                pob
                totalPOB
                totalGuests
                totalPaxJoined
                totalVehiclesJoined
                comment
                fromLocationID
                fromLocation {
                    id
                    title
                }
                fromLat
                fromLong
                toLocationID
                toLocation {
                    id
                    title
                }
                toLat
                toLong
                dangerousGoodsChecklist {
                    id
                }
                sectionSignatureID
                sectionSignature {
                    id
                    signatureData
                }
                enableDGR
                designatedDangerousGoodsSailing
                masterID
                tripReportScheduleID
                logBookEntryID
                logBookEntry {
                    vehicleID
                    vehicle {
                        id
                        title
                    }
                }
                tripEvents(sort: { created: ASC }) {
                    nodes {
                        id
                        eventCategory
                        tripUpdate {
                            id
                            date
                            geoLocationID
                            geoLocation {
                                id
                                title
                            }
                        }
                        eventType_TaskingID
                        eventType_Tasking {
                            id
                            title
                            time
                            type
                            status
                            towingChecklistID
                            towingChecklist {
                                id
                            }
                            fuelLog {
                                nodes {
                                    id
                                    fuelTank {
                                        id
                                    }
                                    fuelAfter
                                }
                            }
                            geoLocationID
                            geoLocation {
                                id
                                title
                            }
                        }
                        eventType_RefuellingBunkeringID
                        eventType_RefuellingBunkering {
                            id
                            fuelLog {
                                nodes {
                                    id
                                    fuelTank {
                                        id
                                    }
                                    fuelAfter
                                    fuelAdded
                                    costPerLitre
                                }
                            }
                        }
                        eventType_PassengerDropFacilityID
                        eventType_PassengerDropFacility {
                            id
                            title
                            type
                            time
                            fuelLog {
                                nodes {
                                    id
                                    fuelTank {
                                        id
                                    }
                                    fuelAfter
                                }
                            }
                        }
                        infringementNotice {
                            id
                            time
                            geoLocationID
                            geoLocation {
                                id
                                title
                            }
                        }
                        incidentRecordID
                        incidentRecord {
                            id
                            title
                            startDate
                        }
                        crewTraining {
                            id
                            startTime
                            geoLocationID
                            geoLocation {
                                id
                                title
                            }
                        }
                        supernumerary {
                            id
                            title
                            totalGuest
                            briefingTime
                            isBriefed
                            guestList {
                                nodes {
                                    id
                                    firstName
                                    surname
                                    sectionSignature {
                                        id
                                        signatureData
                                    }
                                }
                            }
                        }
                        eventType_RestrictedVisibilityID
                        eventType_RestrictedVisibility {
                            id
                            crossingTime
                            startLocationID
                            startLocation {
                                id
                                title
                            }
                        }
                        eventType_BarCrossingID
                        eventType_BarCrossing {
                            id
                            time
                            geoLocationID
                            geoLocation {
                                id
                                title
                            }
                        }
                        eventType_PersonRescueID
                        eventType_PersonRescue {
                            id
                            personName
                            mission {
                                completedAt
                            }
                        }
                        eventType_VesselRescueID
                        eventType_VesselRescue {
                            id
                            vesselName
                            mission {
                                completedAt
                            }
                        }
                    }
                }
                tripReport_Stops {
                    nodes {
                        id
                        lat
                        long
                        arriveTime
                        departTime
                        otherCargo
                        comments
                        paxJoined
                        paxDeparted
                        vehiclesJoined
                        stopLocationID
                        stopLocation {
                            id
                            title
                        }
                    }
                }
            }
        }
    }
`

// VesselDailyCheck_LogBookEntrySection
export const ReadVesselDailyCheck_LogBookEntrySections = gql`
    query ReadVesselDailyCheck_LogBookEntrySections($id: [ID]!) {
        readVesselDailyCheck_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                checkTime
                highWaterAlarm
                safetyEquipment
                lifeRings
                flares
                lifeRaft
                firstAid
                personOverboardRescueEquipment
                epirb
                lifeJackets
                fireHoses
                fireBuckets
                fireBlanket
                fireAxes
                firePump
                fireFlaps
                smokeDetectors
                fireExtinguisher
                crewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                deckOpsCheckTime
                hullStructure
                hull_HullStructure
                pontoonPressure
                bungsInPlace
                hatches
                hull_DeckEquipment
                deckEquipment
                swimPlatformLadder
                biminiTopsCanvasCovers
                dayShapes
                anchor
                windscreenCheck
                nightLineDockLinesRelease
                tenderOperationalChecks
                deckOpsCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                navigationLights
                soundSignallingDevices
                navigationHazards
                vhf
                uhf
                charts
                compass
                wheelhouse
                depthSounder
                radar
                tracPlus
                chartPlotter
                sart
                aisOperational
                gps
                navigationCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                navigationCheckTime
                hvac
                tv
                stabilizationSystems
                electronics
                hvacCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                hvacCheckTime
                forwardAndReverseBelts
                steeringTiller
                unitTransomBolts
                cotterPins
                reverseBucketAndRam
                nozzleAndBearings
                tailHousing
                jetCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                jetCheckTime
                cleanGalleyBench
                cleanGalleyFloor
                cleanTable
                cleanMirrorGlass
                cleanToilet
                cleanSink
                cleanDeckFloor
                cleanOutsideWallWindow
                cleanGarbageBin
                cleanBoothSeat
                cleanFridge
                cleanCupboard
                cleanOven
                cleanSouvenir
                cleanRestockSalesItem
                cleanTill
                exterior
                interior
                cleaningCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                cleaningCheckTime
            }
        }
    }
`

export const CreateAppNotification = gql`
    mutation CreateAppNotification($input: CreateAppNotificationInput!) {
        createAppNotification(input: $input) {
            id
        }
    }
`
