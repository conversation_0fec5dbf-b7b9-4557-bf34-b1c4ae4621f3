'use client'
import { useEffect, useState } from 'react'

import { debounce, isEmpty, trim } from 'lodash'
import { FooterWrapper } from '@/components/footer-wrapper'
import { useLazyQuery, useMutation } from '@apollo/client'
import { ReadOneGeoLocation } from '@/app/lib/graphQL/query'
import {
    CreateGeoLocation,
    DeleteGeoLocations,
    UpdateGeoLocation,
} from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'
import { InputSkeleton } from '../../../components/skeletons'
import LocationMap from '@/components/location-map'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { H2, H3, P } from '@/components/ui/typography'
import { toast } from 'sonner'
import { Check, Trash2, <PERSON><PERSON><PERSON><PERSON>, MapIcon } from 'lucide-react'
import { SealogsLocationIcon } from '@/app/lib/icons'

const GeoLocationForm = ({ id = 0 }: { id?: number }) => {
    const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
    const [isLoading, setIsLoading] = useState(true)
    const router = useRouter()

    const [geoLocation, setGeoLocation] = useState({} as any)
    const [hasFormErrors, setHasFormErrors] = useState(false)
    const [formErrors, setFormErrors] = useState({
        title: '',
    })
    const debouncedhandleInputChange = debounce(
        (name: string, value: string) => {
            setGeoLocation({
                ...geoLocation,
                [name]: value,
                id: +id,
            })
        },
        300,
    )

    const [updateGeoLocation, { loading: loadingUpdateGeoLocation }] =
        useMutation(UpdateGeoLocation, {
            onCompleted: () => {
                router.push(`/location/info?id=${id}`)
            },
            onError: (error: any) => {
                console.error('updateGeoLocation', error)
                toast.error(`Failed to update location: ${error.message}`)
            },
        })
    const [createGeoLocation, { loading: loadingCreateGeoLocation }] =
        useMutation(CreateGeoLocation, {
            onCompleted: () => {
                router.push('/location')
            },
            onError: (error: any) => {
                console.error('createGeoLocation', error)
                toast.error(`Failed to create location: ${error.message}`)
            },
        })
    const [deleteGeoLocation, { loading: loadingDeleteGeoLocation }] =
        useMutation(DeleteGeoLocations, {
            onCompleted: () => {
                setOpenDeleteDialog(false)
                router.push('/location')
            },
            onError: ({ message }: any) => {
                setOpenDeleteDialog(false)
                toast.error(
                    message
                        ? message
                        : 'Cannot delete location. Please remove it from any Trip Report Schedules first.',
                )
            },
        })
    const handleInputChange = (e: any) => {
        const { name, value } = e.target
        if (name === 'lat' || name === 'long') {
            // check if value has a comma
            if (value.includes(',')) {
                toast.error(`You have entered an incorrect value for ${name}`)
                return
            }
        }
        debouncedhandleInputChange(name, value)
    }
    const handleSave = async () => {
        let hasErrors = false
        let errors = {
            title: '',
        }
        if (isEmpty(trim(geoLocation.title))) {
            hasErrors = true
            errors.title = 'Title is required'
        }

        if (hasErrors) {
            setHasFormErrors(true)
            setFormErrors(errors)
            return
        }

        const variables = {
            input: {
                id: +id,
                title: geoLocation.title,
                lat: parseFloat(geoLocation.lat) || 0,
                long: parseFloat(geoLocation.long) || 0,
            },
        }
        if (id === 0) {
            await createGeoLocation({ variables })
        } else {
            await updateGeoLocation({ variables })
        }
    }
    const handleDeleteLocation = async () => {
        await deleteGeoLocation({ variables: { ids: [+id] } })
    }

    const [readOneGeoLocation, { loading: loadingReadOneGeoLocation }] =
        useLazyQuery(ReadOneGeoLocation, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (data: any) => {
                setGeoLocation(data.readOneGeoLocation)
            },
            onError: (error: any) => {
                console.error('readOneGeoLocation', error)
                toast.error(`Failed to load location: ${error.message}`)
            },
        })
    const loadGeoLocation = async () => {
        await readOneGeoLocation({
            variables: {
                id: id,
            },
        })
    }
    const debouncedPositionChange = debounce((position: any) => {
        // Validate position data before updating state
        if (position && Array.isArray(position) && position.length >= 2) {
            const [lat, lng] = position
            if (
                typeof lat === 'number' &&
                typeof lng === 'number' &&
                !isNaN(lat) &&
                !isNaN(lng) &&
                lat >= -90 &&
                lat <= 90 &&
                lng >= -180 &&
                lng <= 180
            ) {
                setGeoLocation((prevLocation: any) => ({
                    ...prevLocation,
                    lat: lat,
                    long: lng,
                }))
            }
        }
    }, 150)

    const handlePositionChange = (position: any) => {
        debouncedPositionChange(position)
    }
    useEffect(() => {
        if (isLoading) {
            loadGeoLocation()
            setIsLoading(false)
        }
    }, [isLoading])
    return (
        <div>
            <Card className="space-y-8 mb-2.5 mx-2.5">
                {/* Header */}
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <SealogsLocationIcon />
                        <H3>{id === 0 ? 'Create' : 'Edit'} Location</H3>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {/* Location Details Section */}
                    <div className="grid grid-cols-1 mb-8 lg:grid-cols-3 gap-6">
                        <div className="space-y-4">
                            <H2 className="text-lg font-medium flex items-center gap-2 text-primary">
                                Location Details
                            </H2>
                            <P className="text-muted-foreground text-sm leading-relaxed">
                                Enter the location name and coordinates. You can
                                also click on the map below to set the
                                coordinates automatically.
                            </P>
                        </div>

                        <div className="col-span-2 space-y-6">
                            <Label
                                label="Location Name"
                                htmlFor="location-name">
                                {loadingReadOneGeoLocation ? (
                                    <InputSkeleton />
                                ) : (
                                    <Input
                                        id="location-name"
                                        name="title"
                                        type="text"
                                        required
                                        defaultValue={geoLocation?.title || ''}
                                        onChange={handleInputChange}
                                        placeholder="Enter location name"
                                    />
                                )}
                                {hasFormErrors && formErrors.title && (
                                    <small className="text-destructive">
                                        {formErrors.title}
                                    </small>
                                )}
                            </Label>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <Label label="Latitude" htmlFor="latitude">
                                    {loadingReadOneGeoLocation ? (
                                        <InputSkeleton />
                                    ) : (
                                        <Input
                                            id="latitude"
                                            name="lat"
                                            type="text"
                                            placeholder="Latitude"
                                            required
                                            defaultValue={
                                                geoLocation?.lat || ''
                                            }
                                            onChange={handleInputChange}
                                        />
                                    )}
                                </Label>

                                <Label label="Longitude" htmlFor="longitude">
                                    {loadingReadOneGeoLocation ? (
                                        <InputSkeleton />
                                    ) : (
                                        <Input
                                            id="longitude"
                                            name="long"
                                            type="text"
                                            placeholder="Longitude"
                                            required
                                            defaultValue={
                                                geoLocation?.long || ''
                                            }
                                            onChange={handleInputChange}
                                        />
                                    )}
                                </Label>
                            </div>

                            {/* <P className="text-muted-foreground text-sm">
                                💡 Tip: Double-click anywhere on the map below
                                to automatically set the coordinates to that
                                location.
                            </P> */}
                        </div>
                    </div>

                    {/* Map Section */}
                    {!loadingReadOneGeoLocation && (
                        <div className="space-y-2.5">
                            <H2 className="text-lg flex gap-2.5 font-medium">
                                <MapIcon /> Location Map
                            </H2>
                            <div className="h-[60svh] w-full rounded-lg overflow-hidden border">
                                <LocationMap
                                    position={[
                                        isNaN(parseFloat(geoLocation?.lat))
                                            ? 0
                                            : parseFloat(geoLocation?.lat),
                                        isNaN(parseFloat(geoLocation?.long))
                                            ? 0
                                            : parseFloat(geoLocation?.long),
                                    ]}
                                    zoom={
                                        geoLocation?.lat &&
                                        geoLocation?.long &&
                                        !isNaN(parseFloat(geoLocation?.lat)) &&
                                        !isNaN(parseFloat(geoLocation?.long))
                                            ? 19
                                            : 2
                                    }
                                    onPositionChange={handlePositionChange}
                                    className="size-full z-0"
                                    // enableClickToSetPosition uncomment if you want to enable double click to set position
                                />
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>
            <FooterWrapper>
                <Button size="sm" variant="back" onClick={() => router.back()}>
                    Cancel
                </Button>

                {id > 0 && (
                    <>
                        <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => setOpenDeleteDialog(true)}
                            isLoading={loadingDeleteGeoLocation}
                            disabled={
                                loadingReadOneGeoLocation ||
                                loadingUpdateGeoLocation ||
                                loadingCreateGeoLocation ||
                                loadingDeleteGeoLocation
                            }>
                            Delete
                        </Button>

                        <AlertDialogNew
                            openDialog={openDeleteDialog}
                            setOpenDialog={setOpenDeleteDialog}
                            handleDestructiveAction={handleDeleteLocation}
                            title="Delete Location"
                            description={`Are you sure you want to delete "${geoLocation?.title}"? This action cannot be undone.`}
                            cancelText="Cancel"
                            destructiveActionText="Delete"
                            showDestructiveAction={true}
                            variant="warning"
                        />
                    </>
                )}
                <Button
                    size="sm"
                    onClick={handleSave}
                    isLoading={
                        loadingUpdateGeoLocation || loadingCreateGeoLocation
                    }
                    disabled={
                        loadingReadOneGeoLocation ||
                        loadingUpdateGeoLocation ||
                        loadingCreateGeoLocation ||
                        loadingDeleteGeoLocation
                    }>
                    {`${id === 0 ? 'Create' : 'Update'} Location`}
                </Button>
            </FooterWrapper>
        </div>
    )
}

export default GeoLocationForm
