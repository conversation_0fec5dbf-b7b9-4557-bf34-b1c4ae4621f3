import gql from 'graphql-tag'

// CREW_BRIEF_LIST
export const ReadSeaLogsMembers = gql`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: <PERSON><PERSON> }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                isArchived
                firstName
                surname
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
                primaryDutyID
                primaryDuty {
                    id
                    title
                }
                hasTrainingSessionDue
                trainingSessionsDue {
                    nodes {
                        id
                        dueDate
                    }
                }
            }
        }
    }
`

export const ReadDepartments = gql`
    query ReadDepartments(
        $filter: DepartmentFilterFields = {}
        $limit: Int = 100
        $offset: Int = 0
    ) {
        readDepartments(filter: $filter, limit: $limit, offset: $offset) {
            nodes {
                id
                title
                parentID
                parent {
                    id
                    title
                }
                children {
                    nodes {
                        id
                        title
                    }
                }
                basicComponents(
                    filter: { componentCategory: { eq: Vehicle } }
                ) {
                    nodes {
                        id
                        title
                        componentCategory
                    }
                }
                seaLogsMembers {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
            }
        }
    }
`
