"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/components/filter/components/training-status-dropdown.tsx":
/*!***********************************************************************!*\
  !*** ./src/components/filter/components/training-status-dropdown.tsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst TrainingStatusDropdown = (param)=>{\n    let { value, onChange, isClearable = false, placeholder = \"Training Status\" } = param;\n    _s();\n    const [selectedValue, setSelectedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const statusOptions = [\n        {\n            value: \"Good\",\n            label: \"Good\"\n        },\n        {\n            value: \"Overdue\",\n            label: \"Overdue\"\n        },\n        {\n            value: \"Due Soon\",\n            label: \"Due Soon\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsLoading(false);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSelectedValue(value);\n    }, [\n        value\n    ]);\n    const handleChange = (selectedOption)=>{\n        setSelectedValue(selectedOption);\n        onChange(selectedOption);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_2__.Combobox, {\n            options: statusOptions,\n            value: selectedValue,\n            onChange: handleChange,\n            title: placeholder,\n            placeholder: placeholder\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\training-status-dropdown.tsx\",\n            lineNumber: 44,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s(TrainingStatusDropdown, \"GxsPtAUIDzJ0B2JPj2yLRU8QpWI=\");\n_c = TrainingStatusDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TrainingStatusDropdown);\nvar _c;\n$RefreshReg$(_c, \"TrainingStatusDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/filter/components/training-status-dropdown.tsx\n"));

/***/ })

});