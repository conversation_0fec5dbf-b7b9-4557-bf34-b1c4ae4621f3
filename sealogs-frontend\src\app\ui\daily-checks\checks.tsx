'use client'

import React, { useEffect, useState } from 'react'
import Safety from './safety/safety'
import Cleaning from './cleaning/cleaning'
import JetSpecific from './jet-specific/jet-specific'
import DailyEngineChecks from '../daily-checks/engine'
import NavigationEquipments from './navigation-eqipments/navigation-eqipments'
import Hull from './hull/hull'
import Plumbing from '../daily-checks/plumbing'
import Hvac from './hvac/hvac'
import {
    Sheet,
    <PERSON>etBody,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import { ScrollArea } from '@/components/ui/scroll-area'
import Task from '../maintenance/task/task'
import {
    CREATE_COMPONENT_MAINTENANCE_CHECK,
    CreateVesselDailyCheck_LogBookEntrySection,
} from '@/app/lib/graphQL/mutation'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useSearchParams } from 'next/navigation'
import {
    GET_SECTION_MEMBER_COMMENTS,
    GET_SECTION_MEMBER_IMAGES,
} from '@/app/lib/graphQL/query'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { useOnline } from '@reactuses/core'
import Biosecurity from './biosecurity'
import { toast } from 'sonner'
import { TabItem } from '@/components/ui/custom-tab'
import ResponsiveTab from '@/components/ui/responsive-tab'

// Removed custom tab classes in favor of Shadcn UI styling
export default function DailyChecks({
    vesselDailyCheck = false,
    logBookConfig = false,
    updateTripReport,
    setVesselDailyCheck = false,
    locked,
    offline = false,
    edit_logBookEntry,
    openCreateTaskSidebar = false,
    setOpenCreateTaskSidebar,
}: {
    vesselDailyCheck: any
    logBookConfig: any
    updateTripReport?: any
    setVesselDailyCheck: any
    locked: boolean
    offline?: boolean
    edit_logBookEntry: boolean
    openCreateTaskSidebar?: boolean
    setOpenCreateTaskSidebar?: any
}) {
    const searchParams = useSearchParams()
    const vesselID = Number(searchParams.get('vesselID')) || 0
    const logentryID = searchParams.get('logentryID') ?? 0
    const [comments, setComments] = useState<any>(false)
    //const [openCreateTaskSidebar, setOpenCreateTaskSidebar] = useState(false)
    const [newTaskID, setNewTaskID] = useState(0)
    const [fieldImages, setFieldImages] = useState<any>(false)
    const sectionMemberCommentModel = new SectionMemberCommentModel()
    const [permissions, setPermissions] = useState<any>(false)
    const [edit_dailyChecks, setEdit_dailyChecks] = useState<any>(false)
    const [edit_task, setEdit_task] = useState<any>(false)
    const online = useOnline()

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_LOGBOKENTRY_DAILY_CHECKS', permissions)) {
                setEdit_dailyChecks(true)
            } else {
                setEdit_dailyChecks(false)
            }

            if (hasPermission('EDIT_TASK', permissions)) {
                setEdit_task(true)
            } else {
                setEdit_task(false)
            }
        }
    }

    const [getFieldImages] = useLazyQuery(GET_SECTION_MEMBER_IMAGES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readCaptureImages.nodes
            if (data) {
                setFieldImages(data)
            }
        },
        onError: (error: any) => {
            console.error('getFieldImages error', error)
        },
    })

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
        getFieldImages({
            variables: {
                filter: {
                    logBookEntryID: { eq: logentryID },
                },
            },
        })
    }, [])

    const refreshImages = async () => {
        await getFieldImages({
            variables: {
                filter: {
                    logBookEntryID: { eq: logentryID },
                },
            },
        })
    }

    useEffect(() => {
        init_permissions()
    }, [permissions])

    const [querySectionMemberComments] = useLazyQuery(
        GET_SECTION_MEMBER_COMMENTS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (!vesselDailyCheck || !vesselDailyCheck[0]) return

        if (offline) {
            const data =
                await sectionMemberCommentModel.getByLogBookEntrySectionID(
                    vesselDailyCheck[0].id,
                )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck[0].id },
                    },
                },
            })
        }
    }

    const createVesselDailyCheck = async () => {
        try {
            await createVesselDailyCheck_LogBookEntrySection({
                variables: {
                    input: {
                        logBookEntryID: logentryID,
                    },
                },
            })
        } catch (error) {
            console.error('Error creating vessel daily check:', error)
            toast.error('Failed to create vessel daily check')
        }
    }

    const [createVesselDailyCheck_LogBookEntrySection] = useMutation(
        CreateVesselDailyCheck_LogBookEntrySection,
        {
            onCompleted: (response: any) => {
                setVesselDailyCheck([
                    response.createVesselDailyCheck_LogBookEntrySection,
                ])
            },
            onError: (error: any) => {
                console.error('createLogEntry error', error)
            },
        },
    )

    const subComponentVisibilityCheck = (category: string) => {
        if (logBookConfig) {
            const components =
                logBookConfig.customisedLogBookComponents?.nodes.find(
                    (c: any) =>
                        c.componentClass ===
                        'VesselDailyCheck_LogBookComponent',
                )
            if (components?.subFields === null) return ''
            if (
                components?.subFields == '' ||
                components?.subFields?.split('||').includes(category)
            ) {
                return ''
            }
            return 'hidden'
        }
        return 'hidden'
    }

    const [createMaintenanceCheck, { loading: createMaintenanceCheckLoading }] =
        useMutation(CREATE_COMPONENT_MAINTENANCE_CHECK, {
            onCompleted: (response: any) => {
                const data = response.createComponentMaintenanceCheck
                if (data) {
                    setNewTaskID(data.id)
                    setOpenCreateTaskSidebar(true)
                }
            },
            onError: (error: any) => {
                console.error('createMaintenanceCheck error', error)
            },
        })
    const handleOfflineClick = (title: any = null) => {
        toast.error(
            `Sorry, you are currently offline. ${title ? 'The ' + title : 'This'} feature is not available in offline mode`,
        )
    }

    const handleCreateTask = async () => {
        if (!online) {
            handleOfflineClick()
            return
        }
        if (!edit_task) {
            toast.error('You do not have permission to edit this section')
            return
        }
        const assignedBy = localStorage.getItem('userId')
        /* if (offline) {
            const id = generateUniqueId()
            const data = {
                id: id,
                name: `New Task ${new Date().toLocaleDateString()}`,
                startDate: new Date().toLocaleDateString(),
                severity: 'Low',
                status: 'Save As Draft',
                assignedByID: assignedBy,
                inventoryID: null,
                basicComponentID: Number(vesselID),
            }
            setNewTaskID(data.id)
            setOpenCreateTaskSidebar(true)
        } else { */
        await createMaintenanceCheck({
            variables: {
                input: {
                    name: `New task ${new Date().toLocaleDateString()}`,
                    startDate: new Date().toLocaleDateString(),
                    severity: 'Low',
                    status: 'Save as draft',
                    assignedByID: assignedBy,
                    inventoryID: null,
                    basicComponentID: Number(vesselID),
                },
            },
        })
        /* } */
    }
    useEffect(() => {
        vesselDailyCheck[0]?.id > 0 && loadSectionMemberComments()
    }, [vesselDailyCheck])

    useEffect(() => {
        if (!vesselDailyCheck) {
            if (offline) {
                const id = generateUniqueId()
                const data = {
                    id: `${id}`,
                    logBookEntryID: logentryID,
                }
                setVesselDailyCheck([data])
            } else {
                createVesselDailyCheck()
            }
        }
    }, [vesselDailyCheck, offline, logentryID])

    const getTabItems = (): TabItem[] => [
        {
            id: 'Safety_checks',
            value: 'Safety Checks',
            label: 'Safety',
            component: vesselDailyCheck[0] && (
                <Safety
                    offline={offline}
                    logBookConfig={logBookConfig}
                    vesselDailyCheck={vesselDailyCheck[0]}
                    comments={comments}
                    setComments={setComments}
                    setVesselDailyCheck={setVesselDailyCheck}
                    locked={locked}
                    // handleCreateTask={handleCreateTask}
                    // createMaintenanceCheckLoading={
                    //     createMaintenanceCheckLoading
                    // }
                    edit_logBookEntry={edit_logBookEntry && edit_dailyChecks}
                    fieldImages={fieldImages}
                    refreshImages={refreshImages}
                />
            ),
        },
        {
            id: 'Deck_operations_&_exterior',
            value: 'Deck operations and exterior checks',
            label: 'Deck operations & exterior',
            component: vesselDailyCheck[0] && (
                <Hull
                    offline={offline}
                    logBookConfig={logBookConfig}
                    vesselDailyCheck={vesselDailyCheck[0]}
                    comments={comments}
                    setComments={setComments}
                    setTab={() => {}}
                    setVesselDailyCheck={setVesselDailyCheck}
                    locked={locked}
                    handleCreateTask={handleCreateTask}
                    createMaintenanceCheckLoading={
                        createMaintenanceCheckLoading
                    }
                    edit_logBookEntry={edit_logBookEntry && edit_dailyChecks}
                    fieldImages={fieldImages}
                    refreshImages={refreshImages}
                />
            ),
        },
        {
            id: 'Engine,_Propulsion,_steering,_electrical_&_alt_power',
            value: 'Engine Checks',
            label: 'Engine, steering, electrical & alt power',
            component: vesselDailyCheck[0] && (
                <DailyEngineChecks
                    offline={offline}
                    logBookConfig={logBookConfig}
                    updateTripReport={updateTripReport}
                    vesselDailyCheck={vesselDailyCheck[0]}
                    comments={comments}
                    setComments={setComments}
                    setTab={() => {}}
                    setVesselDailyCheck={setVesselDailyCheck}
                    locked={locked}
                    handleCreateTask={handleCreateTask}
                    createMaintenanceCheckLoading={
                        createMaintenanceCheckLoading
                    }
                    edit_logBookEntry={edit_logBookEntry && edit_dailyChecks}
                    fieldImages={fieldImages}
                    refreshImages={refreshImages}
                />
            ),
        },
        {
            id: 'Navigation_and_computer_equipment',
            value: 'Navigation',
            label: 'Navigation equipment',
            component: vesselDailyCheck[0] && (
                <NavigationEquipments
                    offline={offline}
                    logBookConfig={logBookConfig}
                    vesselDailyCheck={vesselDailyCheck[0]}
                    comments={comments}
                    setComments={setComments}
                    setTab={() => {}}
                    setVesselDailyCheck={setVesselDailyCheck}
                    locked={locked}
                    handleCreateTask={handleCreateTask}
                    createMaintenanceCheckLoading={
                        createMaintenanceCheckLoading
                    }
                    edit_logBookEntry={edit_logBookEntry && edit_dailyChecks}
                    fieldImages={fieldImages}
                    refreshImages={refreshImages}
                />
            ),
        },
        {
            id: 'hvac',
            value: 'HVAC',
            label: 'HVAC',
            component: vesselDailyCheck[0] && (
                <Hvac
                    offline={offline}
                    logBookConfig={logBookConfig}
                    vesselDailyCheck={vesselDailyCheck[0]}
                    comments={comments}
                    setComments={setComments}
                    setTab={() => {}}
                    setVesselDailyCheck={setVesselDailyCheck}
                    locked={locked}
                    handleCreateTask={handleCreateTask}
                    createMaintenanceCheckLoading={
                        createMaintenanceCheckLoading
                    }
                    edit_logBookEntry={edit_logBookEntry && edit_dailyChecks}
                    fieldImages={fieldImages}
                    refreshImages={refreshImages}
                />
            ),
        },
        {
            id: 'Jet_and_computer_equipment',
            value: 'Jet Specific Checks',
            label: 'Jet Specific',
            component: vesselDailyCheck[0] && (
                <JetSpecific
                    offline={offline}
                    logBookConfig={logBookConfig}
                    vesselDailyCheck={vesselDailyCheck[0]}
                    comments={comments}
                    setComments={setComments}
                    setTab={() => {}}
                    setVesselDailyCheck={setVesselDailyCheck}
                    locked={locked}
                    handleCreateTask={handleCreateTask}
                    createMaintenanceCheckLoading={
                        createMaintenanceCheckLoading
                    }
                    edit_logBookEntry={edit_logBookEntry && edit_dailyChecks}
                    fieldImages={fieldImages}
                    refreshImages={refreshImages}
                />
            ),
        },
        {
            id: 'Cleaning',
            value: 'Cleaning Checks',
            label: 'Cleaning',
            component: vesselDailyCheck[0] && (
                <Cleaning
                    offline={offline}
                    logBookConfig={logBookConfig}
                    vesselDailyCheck={vesselDailyCheck[0]}
                    comments={comments}
                    setComments={setComments}
                    setTab={() => {}}
                    setVesselDailyCheck={setVesselDailyCheck}
                    locked={locked}
                    handleCreateTask={handleCreateTask}
                    createMaintenanceCheckLoading={
                        createMaintenanceCheckLoading
                    }
                    edit_logBookEntry={edit_logBookEntry && edit_dailyChecks}
                    fieldImages={fieldImages}
                    refreshImages={refreshImages}
                />
            ),
        },
        {
            id: 'plumbing',
            value: 'Plumbing',
            label: 'Plumbing',
            component: vesselDailyCheck[0] && (
                <Plumbing
                    offline={offline}
                    logBookConfig={logBookConfig}
                    vesselDailyCheck={vesselDailyCheck[0]}
                    comments={comments}
                    setComments={setComments}
                    setTab={() => {}}
                    setVesselDailyCheck={setVesselDailyCheck}
                    locked={locked}
                    handleCreateTask={handleCreateTask}
                    createMaintenanceCheckLoading={
                        createMaintenanceCheckLoading
                    }
                    edit_logBookEntry={edit_logBookEntry && edit_dailyChecks}
                    fieldImages={fieldImages}
                    refreshImages={refreshImages}
                />
            ),
        },
        {
            id: 'biosecurity',
            value: 'Biosecurity',
            label: 'Biosecurity',
            component: vesselDailyCheck[0] && (
                <Biosecurity
                    offline={offline}
                    logBookConfig={logBookConfig}
                    vesselDailyCheck={vesselDailyCheck[0]}
                    comments={comments}
                    setComments={setComments}
                    setTab={() => {}}
                    setVesselDailyCheck={setVesselDailyCheck}
                    locked={locked}
                    handleCreateTask={handleCreateTask}
                    createMaintenanceCheckLoading={
                        createMaintenanceCheckLoading
                    }
                    edit_logBookEntry={edit_logBookEntry && edit_dailyChecks}
                    fieldImages={fieldImages}
                    refreshImages={refreshImages}
                />
            ),
        },
    ]

    // Get all tabs and filter visible ones based on the visibility check function
    const allTabs = getTabItems()
    const visibleTabs = allTabs.filter(
        (tab) => subComponentVisibilityCheck(tab.value) !== 'hidden',
    )

    return (
        <div className="w-full flex-1 flex flex-col overflow-auto">
            <ResponsiveTab
                tabs={visibleTabs}
                queryParam="checkTab"
                onTabChange={(value) => loadSectionMemberComments()}
            />

            <Sheet
                open={openCreateTaskSidebar}
                onOpenChange={setOpenCreateTaskSidebar}>
                <SheetContent side="left" className="w-[90vw] sm:w-[600px] p-0">
                    <SheetHeader>
                        <SheetTitle>Task Details</SheetTitle>
                    </SheetHeader>
                    <ScrollArea className="p-0">
                        <SheetBody>
                            <Task
                                taskId={+newTaskID}
                                redirectTo=""
                                inSidebar
                                onSidebarClose={() =>
                                    setOpenCreateTaskSidebar(false)
                                }
                                vesselID={vesselID}
                            />
                        </SheetBody>
                    </ScrollArea>
                </SheetContent>
            </Sheet>
        </div>
    )
}
